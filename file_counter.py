#!/usr/bin/env python3
"""
File Counter Script
Counts all files in a given directory path including subdirectories.
"""

import os
import sys
from pathlib import Path


def count_files_in_directory(directory_path):
    """
    Count all files in the given directory and its subdirectories.
    
    Args:
        directory_path (str): The path to the directory to count files in
        
    Returns:
        tuple: (total_files, file_details) where file_details is a dict with breakdown
    """
    if not os.path.exists(directory_path):
        print(f"Error: Directory '{directory_path}' does not exist.")
        return 0, {}
    
    if not os.path.isdir(directory_path):
        print(f"Error: '{directory_path}' is not a directory.")
        return 0, {}
    
    total_files = 0
    file_details = {
        'directories': 0,
        'files_by_extension': {},
        'files_by_directory': {}
    }
    
    try:
        # Walk through all directories and subdirectories
        for root, dirs, files in os.walk(directory_path):
            file_details['directories'] += 1
            file_count_in_dir = len(files)
            file_details['files_by_directory'][root] = file_count_in_dir
            total_files += file_count_in_dir
            
            # Count files by extension
            for file in files:
                file_path = os.path.join(root, file)
                if os.path.isfile(file_path):  # Double check it's actually a file
                    _, ext = os.path.splitext(file)
                    ext = ext.lower() if ext else 'no_extension'
                    file_details['files_by_extension'][ext] = file_details['files_by_extension'].get(ext, 0) + 1
    
    except PermissionError as e:
        print(f"Permission denied accessing some files/directories: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")
    
    return total_files, file_details


def print_detailed_report(directory_path, total_files, file_details):
    """Print a detailed report of the file count."""
    print(f"\n{'='*60}")
    print(f"FILE COUNT REPORT")
    print(f"{'='*60}")
    print(f"Directory: {directory_path}")
    print(f"Total Files: {total_files}")
    print(f"Total Directories: {file_details['directories']}")
    
    # Print files by extension
    if file_details['files_by_extension']:
        print(f"\n{'Files by Extension:':<30}")
        print(f"{'-'*30}")
        for ext, count in sorted(file_details['files_by_extension'].items()):
            ext_display = ext if ext != 'no_extension' else '(no extension)'
            print(f"{ext_display:<20} {count:>8}")
    
    # Print top directories with most files
    if file_details['files_by_directory']:
        print(f"\n{'Top 10 Directories by File Count:':<50}")
        print(f"{'-'*50}")
        sorted_dirs = sorted(file_details['files_by_directory'].items(), 
                           key=lambda x: x[1], reverse=True)[:10]
        for dir_path, count in sorted_dirs:
            # Truncate long paths for display
            display_path = dir_path if len(dir_path) <= 40 else "..." + dir_path[-37:]
            print(f"{display_path:<40} {count:>8}")


def main():
    """Main function to run the file counter."""
    # Hardcoded path - modify this to your desired directory
    HARDCODED_PATH = "/Users/<USER>/Library/CloudStorage/OneDrive-easyguide/PythonScripts"
    
    # You can also accept command line arguments
    if len(sys.argv) > 1:
        directory_path = sys.argv[1]
        print(f"Using command line argument: {directory_path}")
    else:
        directory_path = HARDCODED_PATH
        print(f"Using hardcoded path: {directory_path}")
    
    # Convert to absolute path
    directory_path = os.path.abspath(directory_path)
    
    print(f"Counting files in: {directory_path}")
    print("Please wait...")
    
    # Count files
    total_files, file_details = count_files_in_directory(directory_path)
    
    if total_files > 0:
        print_detailed_report(directory_path, total_files, file_details)
    else:
        print("No files found or an error occurred.")


if __name__ == "__main__":
    main()
